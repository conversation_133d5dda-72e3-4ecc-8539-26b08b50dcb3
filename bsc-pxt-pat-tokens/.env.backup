# BSC测试网环境配置文件
# 复制此文件为 .env 并填入真实的私钥和API密钥

# ===== 网络配置 =====
NETWORK=bscTestnet
BSC_TESTNET_RPC=https://data-seed-prebsc-2-s1.binance.org:8545
BSC_TESTNET_RPC_BACKUP=https://data-seed-prebsc-1-s1.binance.org:8545
BSC_TESTNET_RPC_BACKUP2=https://data-seed-prebsc-2-s1.binance.org:8545

# ===== 账户私钥 (请替换为你的真实私钥) =====
# 部署者账户 - 需要有足够的tBNB用于部署
DEPLOYER_PRIVATE_KEY=0x2b0538b9bff355acf4a6fb175497e3276406512958c6caba27f11e814f5df191

# 国库账户 - 用于接收代币和管理资金
TREASURY_PRIVATE_KEY=0x7a410f3581115ab85eeb5de43058fa02795550dd5c9797cc6edf1c74d63a09cb

# 操作员账户 - 用于日常操作和管理
OPERATOR_PRIVATE_KEY=0x79921ff137263db45287ff53f52a5616c66374c6f4e1b86efe4c10d5295db598

# 测试用户私钥 (可选，用于测试)
TEST_USER_1_PRIVATE_KEY=0x538a3db910cf5746b533dc281dd21a83535a80a1270893f90ea4156900682d32
TEST_USER_2_PRIVATE_KEY=0xdbda78a1f6706956973c60d62b6efc2ee9c382c366c9cd1321e6b305a1a73c58
TEST_USER_3_PRIVATE_KEY=0x13f97ac3c08f05fa17e34075e076356973d32fd7f4ca63518028f41dcaa61602

# ===== API密钥 =====
# BSCScan API密钥 (用于合约验证)
BSCSCAN_API_KEY=**********************************

# ===== IPFS配置 =====
# Pinata IPFS API配置
PINATA_API_KEY=121a274e13e83493e1d1
PINATA_SECRET_API_KEY=****************************************************************
PINATA_JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qge86CAThtvlaOc5KoWfZ2uLIsF0tpbgBqTXTF-KE0k

# IPFS网关配置
IPFS_GATEWAY=https://gateway.pinata.cloud/ipfs/
IPFS_UPLOAD_ENDPOINT=https://api.pinata.cloud/pinning/pinFileToIPFS
IPFS_JSON_ENDPOINT=https://api.pinata.cloud/pinning/pinJSONToIPFS

# ===== Gas配置 =====
# Gas价格 (单位: wei, 20 gwei = 20000000000)
GAS_PRICE=20000000000

# Gas限制
GAS_LIMIT=8000000

# 最小确认数
MIN_CONFIRMATIONS=2

# ===== 部署配置 =====
# 代币初始分配 (单位: ether)
PXT_TOTAL_SUPPLY=100000000
PAT_TOTAL_SUPPLY=300000000

# 池子分配
CHINA_MAINLAND_POOL_PXT=19998000
CHINA_MAINLAND_POOL_PAT=50000000
GLOBAL_POOL_PAT=50000000
STAKING_REWARD_POOL_PAT=200000000

# 质押配置
MIN_STAKE_AMOUNT=1
BASE_APY=500

# ===== 安全配置 =====
# 是否启用多重签名 (true/false)
ENABLE_MULTISIG=false

# 是否启用时间锁 (true/false)
ENABLE_TIMELOCK=false

# ===== 监控配置 =====
# 是否启用事件监控
ENABLE_MONITORING=true

# 监控间隔 (秒)
MONITORING_INTERVAL=60

# ===== 其他配置 =====
# 部署标签
DEPLOYMENT_TAG=bsc-testnet-v1.0.0

# 是否保存部署信息
SAVE_DEPLOYMENTS=true

# 调试模式
DEBUG=false

# ===== 内容上链合约地址 (BSC测试网) - 更新版本 =====
# 内容注册合约 - 主要的内容上链合约 (修复IPFS验证)
CONTENT_REGISTRY_ADDRESS=******************************************

# 内容身份合约 - 用户Character NFT
CONTENT_CHARACTER_ADDRESS=******************************************

# 内容铸造合约 - 内容NFT铸造
CONTENT_MINT_ADDRESS=0x639dF417ca19545553B46CA4dDF53a6dC4C80b47
