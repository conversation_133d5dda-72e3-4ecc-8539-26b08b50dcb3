require("dotenv").config();
require("@nomicfoundation/hardhat-ethers");
require("@nomicfoundation/hardhat-verify");
require("@nomicfoundation/hardhat-chai-matchers");
require("hardhat-gas-reporter");
require("solidity-coverage");

// 从环境变量获取私钥和API密钥 - 强制使用环境变量
const DEPLOYER_PRIVATE_KEY = process.env.DEPLOYER_PRIVATE_KEY;
const TREASURY_PRIVATE_KEY = process.env.TREASURY_PRIVATE_KEY;
const OPERATOR_PRIVATE_KEY = process.env.OPERATOR_PRIVATE_KEY;

// 测试用户私钥 - 强制使用环境变量
const TEST_USER_1_PRIVATE_KEY = process.env.TEST_USER_1_PRIVATE_KEY;
const TEST_USER_2_PRIVATE_KEY = process.env.TEST_USER_2_PRIVATE_KEY;
const TEST_USER_3_PRIVATE_KEY = process.env.TEST_USER_3_PRIVATE_KEY;

// 验证环境变量是否存在
if (!DEPLOYER_PRIVATE_KEY) {
  throw new Error("DEPLOYER_PRIVATE_KEY 环境变量未设置");
}
if (!TREASURY_PRIVATE_KEY) {
  throw new Error("TREASURY_PRIVATE_KEY 环境变量未设置");
}
if (!OPERATOR_PRIVATE_KEY) {
  throw new Error("OPERATOR_PRIVATE_KEY 环境变量未设置");
}

// 网络RPC配置 - 从环境变量读取，如果未设置则使用默认值
const BSC_TESTNET_RPC = process.env.BSC_TESTNET_RPC || "https://data-seed-prebsc-1-s1.binance.org:8545";
const BSC_MAINNET_RPC = process.env.BSC_MAINNET_RPC || "https://bsc-dataseed.binance.org/";

// API密钥 - 从环境变量读取
const BSCSCAN_API_KEY = process.env.BSCSCAN_API_KEY || "";

// Gas配置 - 从环境变量读取，如果未设置则使用默认值
const TESTNET_GAS_PRICE = process.env.GAS_PRICE || "***********"; // 20 gwei
const TESTNET_GAS_LIMIT = process.env.GAS_LIMIT || "8000000"; // 8M gas

// 配置任务
task("accounts", "打印所有账户", async (taskArgs, hre) => {
  const accounts = await hre.ethers.getSigners();
  for (const account of accounts) {
    console.log(account.address);
  }
});

// 新增：检查测试网余额任务
task("check-balances", "检查测试网账户余额", async (taskArgs, hre) => {
  const accounts = await hre.ethers.getSigners();
  console.log("=== 测试网账户余额检查 ===");
  for (let i = 0; i < Math.min(accounts.length, 6); i++) {
    const account = accounts[i];
    const balance = await account.provider.getBalance(account.address);
    const balanceInBNB = hre.ethers.formatEther(balance);
    const role = i === 0 ? "部署者" : i === 1 ? "国库" : i === 2 ? "操作员" : `测试用户${i-2}`;
    console.log(`${role}: ${account.address} - ${balanceInBNB} BNB`);
  }
});

module.exports = {
  solidity: {
    version: "0.8.24",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200  // 降低runs值以减小合约大小
      },
      viaIR: true,
      debug: {
        revertStrings: "strip"  // 移除revert字符串以减小大小
      },
      metadata: {
        bytecodeHash: "none"  // 减少部署大小
      },
      // 添加编译输出配置，减少警告
      outputSelection: {
        "*": {
          "*": ["abi", "evm.bytecode", "evm.deployedBytecode"]
        }
      }
    }
  },
  
  networks: {
    // 本地开发网络
    hardhat: {
      chainId: 31337,
      loggingEnabled: false,
      throwOnTransactionFailures: true,
      throwOnCallFailures: true,
      mining: {
        auto: true,
        interval: 0
      }
    },
    // 本地测试网
    localhost: {
      url: "http://127.0.0.1:18485",  // BSC本地链端口
      chainId: 97,  // BSC本地链ID
      accounts: [
        DEPLOYER_PRIVATE_KEY,  // 部署者账户 - 从.env文件读取
        TREASURY_PRIVATE_KEY,  // 国库账户 - 从.env文件读取
        OPERATOR_PRIVATE_KEY,  // 操作员账户 - 从.env文件读取
        TEST_USER_1_PRIVATE_KEY,  // 测试用户1 - 从.env文件读取
        TEST_USER_2_PRIVATE_KEY,  // 测试用户2 - 从.env文件读取
        TEST_USER_3_PRIVATE_KEY   // 测试用户3 - 从.env文件读取
      ].filter(key => key && key !== "")
    },
    // 本地BSC链
    localBsc: {
      url: "http://127.0.0.1:18485",  // BSC本地链端口
      chainId: 97,  // BSC链ID
      accounts: [
        DEPLOYER_PRIVATE_KEY,  // 部署者账户 - 从.env文件读取
        TREASURY_PRIVATE_KEY,  // 国库账户 - 从.env文件读取
        OPERATOR_PRIVATE_KEY,  // 操作员账户 - 从.env文件读取
        TEST_USER_1_PRIVATE_KEY,  // 测试用户1 - 从.env文件读取
        TEST_USER_2_PRIVATE_KEY,  // 测试用户2 - 从.env文件读取
        TEST_USER_3_PRIVATE_KEY   // 测试用户3 - 从.env文件读取
      ].filter(key => key && key !== "")
    },
    // BSC测试网 - 主配置
    bscTestnet: {
      url: BSC_TESTNET_RPC,
      chainId: 97,
      gasPrice: parseInt(TESTNET_GAS_PRICE),
      gas: parseInt(TESTNET_GAS_LIMIT),
      accounts: [
        DEPLOYER_PRIVATE_KEY,
        TREASURY_PRIVATE_KEY,
        OPERATOR_PRIVATE_KEY,
        TEST_USER_1_PRIVATE_KEY,
        TEST_USER_2_PRIVATE_KEY,
        TEST_USER_3_PRIVATE_KEY
      ].filter(key => key && key !== ""),
      timeout: 180000, // 3分钟超时
      httpHeaders: {
        "User-Agent": "hardhat"
      },
      allowUnlimitedContractSize: true,
      blockGasLimit: ********,
      timeout: 300000, // 5分钟
      confirmations: parseInt(process.env.MIN_CONFIRMATIONS || "2"),
      saveDeployments: true,
      tags: ["testnet"]
    },
    // BSC测试网 - 备用配置
    testnet: {
      url: BSC_TESTNET_RPC,
      chainId: 97,
      gasPrice: parseInt(TESTNET_GAS_PRICE),
      gas: parseInt(TESTNET_GAS_LIMIT),
      accounts: [
        DEPLOYER_PRIVATE_KEY,
        TREASURY_PRIVATE_KEY,
        OPERATOR_PRIVATE_KEY,
        TEST_USER_1_PRIVATE_KEY,
        TEST_USER_2_PRIVATE_KEY,
        TEST_USER_3_PRIVATE_KEY
      ].filter(key => key && key !== ""),
      timeout: 300000,
      confirmations: parseInt(process.env.MIN_CONFIRMATIONS || "2"),
      saveDeployments: true
    },
    // BSC主网
    bscMainnet: {
      url: BSC_MAINNET_RPC,
      chainId: 56,
      gasPrice: **********, // 5 gwei
      gas: 5000000, // 5M gas限制
      accounts: [DEPLOYER_PRIVATE_KEY].filter(key => key && key !== ""),
      timeout: 120000, // 2分钟
      confirmations: 3, // 等待3个确认
      saveDeployments: true,
      tags: ["mainnet"]
    },

    // PXA本地链配置 (用于跨链测试)
    pxaLocal: {
      url: "http://127.0.0.1:8545", // PXA本地链端口
      chainId: 327, // PXA链ID
      gasPrice: **********, // 1 gwei (极低费用)
      gas: ********, // 20M gas限制
      accounts: [
        DEPLOYER_PRIVATE_KEY,
        TREASURY_PRIVATE_KEY,
        OPERATOR_PRIVATE_KEY,
        TEST_USER_1_PRIVATE_KEY,
        TEST_USER_2_PRIVATE_KEY,
        TEST_USER_3_PRIVATE_KEY
      ].filter(key => key && key !== ""),
      timeout: 120000,
      confirmations: 1, // 本地链快速确认
      saveDeployments: true,
      tags: ["pxa", "local", "crosschain"]
    },

    // 预留：PXA主网配置 (未来使用)
    pxaMainnet: {
      url: process.env.PXA_MAINNET_RPC || "https://rpc.pxa.network",
      chainId: parseInt(process.env.PXA_CHAIN_ID || "327"),
      gasPrice: **********, // 1 gwei
      gas: ********,
      accounts: [DEPLOYER_PRIVATE_KEY].filter(key => key && key !== ""),
      timeout: 120000,
      confirmations: 2,
      saveDeployments: true,
      tags: ["pxa", "mainnet"]
    },

    // Polygon配置 (备用跨链选项)
    polygon: {
      url: process.env.POLYGON_RPC || "https://polygon-rpc.com/",
      chainId: 137,
      gasPrice: ***********, // 30 gwei
      gas: 5000000,
      accounts: [DEPLOYER_PRIVATE_KEY].filter(key => key && key !== ""),
      timeout: 120000,
      confirmations: 2,
      saveDeployments: true,
      tags: ["polygon", "sidechain"]
    }
  },
  // 验证配置 (替代 etherscan)
  sourcify: {
    enabled: true
  },
  etherscan: {
    apiKey: {
      bscTestnet: BSCSCAN_API_KEY,
      bsc: BSCSCAN_API_KEY,
    },
    customChains: [
      {
        network: "bscTestnet",
        chainId: 97,
        urls: {
          apiURL: "https://api-testnet.bscscan.com/api",
          browserURL: "https://testnet.bscscan.com"
        }
      }
    ]
  },
  // Gas报告配置
  gasReporter: {
    enabled: process.env.REPORT_GAS !== undefined,
    currency: "USD",
    coinmarketcap: process.env.COINMARKETCAP_API_KEY
  },
  // 路径配置
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts",
    deployments: "./deployments",
    deploy: "./scripts/deploy"
  },
  // Mocha配置
  mocha: {
    timeout: 300000, // 增加测试超时到5分钟
    quiet: false
  },
  
  // 部署配置
  namedAccounts: {
    deployer: {
      default: 0,
      testnet: 0,
      bscTestnet: 0,
      mainnet: 0
    },
    treasury: {
      default: 1,
      testnet: 1,
      bscTestnet: 1,
      mainnet: 1
    },
    operator: {
      default: 2,
      testnet: 2,
      bscTestnet: 2,
      mainnet: 2
    }
  }
};
